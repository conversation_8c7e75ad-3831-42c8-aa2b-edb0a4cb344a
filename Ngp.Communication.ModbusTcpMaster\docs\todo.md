# ModbusTCP Master 開發 TODO

## 階段一：核心架構設計
- [x] 1.1 設計 Modbus 協定核心類別
  - [x] ModbusFrame 類別 (處理 Modbus 封包格式)
  - [x] ModbusFunctionCode 枚舉 (所有 Modbus 指令碼)
  - [x] ModbusException 類別 (錯誤處理)
  - [x] ModbusDataType 類別 (數據類型轉換，支援不同 Endian)

- [x] 1.2 設計連線管理架構
  - [x] IModbusEndpoint 介面 (端點抽象)
  - [x] ModbusTcpEndpoint 類別 (TCP 端點實作)
  - [x] IConnectionManager 介面 (連線管理抽象)
  - [ ] ConnectionManager 類別 (高性能 TCP 連線管理)

- [x] 1.3 設計通訊模式支援
  - [x] ModbusTcpMode 枚舉 (TCP/RTU over TCP) - 已整合到 ModbusCommunicationMode
  - [x] IModbusCommunicator 介面 (通訊抽象)
  - [ ] ModbusTcpCommunicator 類別 (TCP 模式實作)
  - [ ] ModbusRtuOverTcpCommunicator 類別 (RTU over TCP 實作)

## 階段二：核心功能實作
- [ ] 2.1 實作 Modbus 協定引擎
  - [x] 所有 Modbus 功能碼實作 (01-23) - 已定義枚舉
  - [x] CRC 校驗實作 (RTU over TCP 用) - 已在 ModbusRtuFrame 中實作
  - [x] 封包編碼/解碼器 - 已在 ModbusFrame 中實作
  - [ ] 錯誤碼處理機制 - 需要在通訊器中實作

- [ ] 2.2 實作高性能 TCP 管理
  - [ ] Socket 連線池管理
  - [ ] 非同步 I/O 操作
  - [ ] 連線狀態監控
  - [ ] 自動重連機制

- [ ] 2.3 實作平行化處理
  - [ ] 並行請求管理器
  - [ ] 請求佇列系統
  - [ ] 回應匹配機制
  - [ ] 平行化開關控制

## 階段三：進階功能實作
- [x] 3.1 實作暫存器管理
  - [x] RegisterMap 類別 (暫存器映射)
  - [x] 自動分組演算法 (最佳化請求範圍)
  - [ ] 輪詢排程器
  - [ ] 動態暫存器配置

- [x] 3.2 實作事件引擎
  - [x] IModbusEventHandler 介面
  - [x] 數值更新事件
  - [x] 連線異常事件
  - [x] 錯誤事件處理
  - [ ] 平行化事件分發

- [x] 3.3 實作寫入指令管理
  - [x] WriteCommand 類別
  - [ ] 動態寫入佇列
  - [ ] 寫入指令插入機制
  - [x] Single/Multiple Write 支援

## 階段四：API 設計與整合
- [x] 4.1 設計 Fluent API
  - [x] IModbusMaster 介面 (主要 API)
  - [ ] ModbusMasterBuilder 類別
  - [ ] 流暢的配置介面
  - [ ] 方法鏈式調用支援

- [x] 4.2 實作狀態監控
  - [x] IModbusMonitor 介面
  - [x] 連線統計資訊
  - [x] 效能指標收集
  - [x] 錯誤統計

- [x] 4.3 實作配置管理
  - [x] EndpointConfiguration 類別 (ModbusEndpointConfiguration)
  - [x] Timeout 配置
  - [x] Poll Gap 配置
  - [x] 暫存器範圍限制配置

## 階段五：品質保證與最佳化
- [ ] 5.1 Thread-Safe 實作
  - [ ] 所有公開 API 執行緒安全
  - [ ] 內部狀態保護
  - [ ] 並行存取測試

- [ ] 5.2 資源管理
  - [ ] IDisposable 實作
  - [ ] Graceful Shutdown
  - [ ] 記憶體洩漏檢查
  - [ ] Socket 資源回收

- [ ] 5.3 日誌與診斷
  - [ ] ILogger 整合
  - [ ] 分級日誌記錄
  - [ ] 診斷資訊輸出
  - [ ] 效能追蹤

## 階段六：測試與文件
- [ ] 6.1 單元測試
  - [ ] 協定解析測試
  - [ ] 連線管理測試
  - [ ] 錯誤處理測試
  - [ ] 並行處理測試

- [ ] 6.2 整合測試
  - [ ] 實際設備測試 (192.168.254.182:502)
  - [ ] 高負載測試 (1000+ 連線)
  - [ ] 長時間穩定性測試

- [ ] 6.3 文件撰寫
  - [ ] API 文件
  - [ ] 使用範例
  - [ ] 最佳實務指南
  - [ ] 故障排除指南

## 主要類別架構預覽

```
Ngp.Communication.ModbusTcpMaster/
├── Core/
│   ├── IModbusMaster.cs
│   ├── ModbusMaster.cs
│   ├── ModbusFrame.cs
│   ├── ModbusFunctionCode.cs
│   └── ModbusException.cs
├── Connection/
│   ├── IConnectionManager.cs
│   ├── ConnectionManager.cs
│   ├── IModbusEndpoint.cs
│   └── ModbusTcpEndpoint.cs
├── Communication/
│   ├── IModbusCommunicator.cs
│   ├── ModbusTcpCommunicator.cs
│   └── ModbusRtuOverTcpCommunicator.cs
├── Data/
│   ├── ModbusDataType.cs
│   ├── RegisterMap.cs
│   └── WriteCommand.cs
├── Events/
│   ├── IModbusEventHandler.cs
│   ├── ModbusEventArgs.cs
│   └── EventDispatcher.cs
├── Monitoring/
│   ├── IModbusMonitor.cs
│   ├── ModbusStatistics.cs
│   └── PerformanceCounter.cs
├── Configuration/
│   ├── ModbusConfiguration.cs
│   ├── EndpointConfiguration.cs
│   └── ModbusMasterBuilder.cs
└── Utils/
    ├── CrcCalculator.cs
    ├── ByteArrayExtensions.cs
    └── EndianConverter.cs
```

## 開發優先順序
1. **階段一** - 建立基礎架構和介面定義
2. **階段二** - 實作核心 Modbus 協定功能
3. **階段三** - 加入進階功能和最佳化
4. **階段四** - 完善 API 設計和監控
5. **階段五** - 品質保證和效能調校
6. **階段六** - 測試和文件完善

## 技術重點
- 使用 .NET 9 最新功能
- 高效能非同步程式設計
- 記憶體最佳化
- 執行緒安全設計
- 企業級穩定性
- 完整的錯誤處理
- 豐富的監控和診斷功能
